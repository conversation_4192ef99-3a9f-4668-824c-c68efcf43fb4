import React, { useState, useRef } from 'react';

const RollingText = ({ text, className = "", delay = 0.015, duration = 0.6, ...props }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const timeoutRef = useRef(null);
  const letters = text.split('');

  const handleMouseEnter = () => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsHovered(true);
    setIsAnimating(true);
  };

  const handleMouseLeave = () => {
    // Delay the mouse leave to allow the animation to complete and stay visible briefly
    timeoutRef.current = setTimeout(() => {
      setIsHovered(false);
      // Reset animation state after the roll-back animation completes
      setTimeout(() => {
        setIsAnimating(false);
      }, (duration * 1000) + (letters.length * delay * 1000));
    }, 200); // Stay visible for 200ms after mouse leave
  };

  // Clean up timeout on unmount
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <a
      className={`rolling-text inline-block overflow-hidden cursor-pointer ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        height: '1.5em',
        lineHeight: '1.5em',
        textDecoration: 'none'
      }}
      {...props}
    >
      <div
        className="block transition-transform ease-out"
        style={{
          transitionDuration: `${duration}s`,
          transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
          transform: (isHovered || isAnimating) ? 'translateY(-100%)' : 'translateY(0%)'
        }}
      >
        {letters.map((letter, i) => (
          <span
            key={`first-${i}`}
            className="letter inline-block transition-transform ease-out"
            style={{
              transitionDelay: isHovered ? `${i * delay}s` : `${(letters.length - 1 - i) * delay}s`,
              transitionDuration: `${duration}s`,
              transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
              transform: (isHovered || isAnimating) ? 'translateY(-100%)' : 'translateY(0%)'
            }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </span>
        ))}
      </div>
      <div
        className="block transition-transform ease-out text-green-200"
        style={{
          transitionDuration: `${duration}s`,
          transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
          transform: (isHovered || isAnimating) ? 'translateY(-100%)' : 'translateY(0%)'
        }}
      >
        {letters.map((letter, i) => (
          <span
            key={`second-${i}`}
            className="letter inline-block transition-transform ease-out"
            style={{
              transitionDelay: isHovered ? `${i * delay}s` : `${(letters.length - 1 - i) * delay}s`,
              transitionDuration: `${duration}s`,
              transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
              transform: (isHovered || isAnimating) ? 'translateY(-100%)' : 'translateY(0%)'
            }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </span>
        ))}
      </div>
    </a>
  );
};

export default RollingText;
