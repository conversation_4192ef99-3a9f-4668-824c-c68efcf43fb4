import React, { useState } from 'react';

const RollingText = ({ text, className = "", delay = 0.015, duration = 0.6, ...props }) => {
  const [isHovered, setIsHovered] = useState(false);
  const letters = text.split('');

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <a
      className={`rolling-text inline-block overflow-hidden cursor-pointer ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        height: '1.5em',
        lineHeight: '1.5em',
        textDecoration: 'none'
      }}
      {...props}
    >
      <div
        className="block transition-transform ease-out"
        style={{
          transitionDuration: `${duration}s`,
          transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
          transform: isHovered ? 'translateY(-100%)' : 'translateY(0%)'
        }}
      >
        {letters.map((letter, i) => (
          <span
            key={`first-${i}`}
            className="letter inline-block transition-transform ease-out"
            style={{
              transitionDelay: `${i * delay}s`,
              transitionDuration: `${duration}s`,
              transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
              transform: isHovered ? 'translateY(-100%)' : 'translateY(0%)'
            }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </span>
        ))}
      </div>
      <div
        className="block transition-transform ease-out text-green-200"
        style={{
          transitionDuration: `${duration}s`,
          transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
          transform: isHovered ? 'translateY(-100%)' : 'translateY(0%)'
        }}
      >
        {letters.map((letter, i) => (
          <span
            key={`second-${i}`}
            className="letter inline-block transition-transform ease-out"
            style={{
              transitionDelay: `${i * delay}s`,
              transitionDuration: `${duration}s`,
              transitionTimingFunction: 'cubic-bezier(0.76, 0, 0.24, 1)',
              transform: isHovered ? 'translateY(-100%)' : 'translateY(0%)'
            }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </span>
        ))}
      </div>
    </a>
  );
};

export default RollingText;
