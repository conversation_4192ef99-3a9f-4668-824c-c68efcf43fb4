import React from 'react';

const RollingText = ({ text, ...props }) => {
  const letters = text.split('');

  return (
    <a className="rolling-text" {...props}>
      <div className="block">
        {letters.map((letter, i) => (
          <span
            key={i}
            className="letter"
            style={{ transitionDelay: `${i * 0.015}s` }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </span>
        ))}
      </div>
      <div className="block">
        {letters.map((letter, i) => (
          <span
            key={i}
            className="letter"
            style={{ transitionDelay: `${i * 0.015}s` }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </span>
        ))}
      </div>
    </a>
  );
};

export default RollingText;
