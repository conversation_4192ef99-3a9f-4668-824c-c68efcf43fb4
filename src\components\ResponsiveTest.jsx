import React, { useState, useEffect } from 'react';

const ResponsiveTest = () => {
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getBreakpoint = () => {
    if (screenSize.width < 640) return 'xs (Mobile)';
    if (screenSize.width < 768) return 'sm (Small)';
    if (screenSize.width < 1024) return 'md (Tablet)';
    if (screenSize.width < 1280) return 'lg (Desktop)';
    if (screenSize.width < 1536) return 'xl (Large)';
    return '2xl (Extra Large)';
  };

  const checkHorizontalScroll = () => {
    return document.documentElement.scrollWidth > document.documentElement.clientWidth;
  };

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs z-[9999] backdrop-blur-sm">
      <div>Screen: {screenSize.width}x{screenSize.height}</div>
      <div>Breakpoint: {getBreakpoint()}</div>
      <div className={`${checkHorizontalScroll() ? 'text-red-400' : 'text-green-400'}`}>
        H-Scroll: {checkHorizontalScroll() ? 'YES ⚠️' : 'NO ✅'}
      </div>
    </div>
  );
};

export default ResponsiveTest;
