@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global CSS Reset and Overflow Control */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  max-width: 100vw;
  margin: 0;
  padding: 0;
}

#root {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Prevent horizontal scrolling from any element */
* {
  max-width: 100%;
}

/* Additional overflow prevention */
.container, .w-full, [class*="w-"] {
  max-width: 100vw;
}

/* Ensure no element can cause horizontal scroll */
body * {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Prevent specific elements from overflowing */
pre, code {
  white-space: pre-wrap;
  word-break: break-all;
}

/* Ensure images don't overflow */
img {
  max-width: 100%;
  height: auto;
}

/* Responsive text utilities */
@layer utilities {
  .text-responsive-xs {
    font-size: clamp(0.75rem, 2vw, 1rem);
  }

  .text-responsive-sm {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
  }

  .text-responsive-base {
    font-size: clamp(1rem, 3vw, 1.25rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.125rem, 4vw, 1.5rem);
  }

  .text-responsive-xl {
    font-size: clamp(1.25rem, 5vw, 2rem);
  }

  .text-responsive-2xl {
    font-size: clamp(1.5rem, 6vw, 3rem);
  }

  .text-responsive-3xl {
    font-size: clamp(1.875rem, 8vw, 4rem);
  }

  .text-responsive-4xl {
    font-size: clamp(2.25rem, 10vw, 6rem);
  }
}

@font-face {
    font-family: 'MyCustomFont';
    src: url('./assets/FoundersGrotesk-Semibold.ttf') format('truetype');
}

@font-face {
    font-family: 'MyCustomFont2';
    src: url('./assets/icomoon.ttf') format('truetype');
}

@font-face {
    font-family: 'MyCustomFont3';
    src: url('./assets/NeueMontreal-Regular.ttf') format('truetype');
}

@import url('https://fonts.googleapis.com/css2?family=Playfair+Display&display=swap');

.rolling-text {
  --font-size: 1.125rem;
  --line-height: 1.5;
  position: relative;
  display: inline-block;
  font-family: 'Playfair Display', serif;
  font-size: var(--font-size);
  line-height: var(--line-height);
  height: calc(var(--font-size) * var(--line-height));
  text-decoration: none;
  overflow: hidden;
  color: white;
}

.rolling-text .block {
  display: inline-block;
  transition: transform 0.6s cubic-bezier(0.76, 0, 0.24, 1);
}

.rolling-text .block:last-child {
  position: absolute;
  top: 100%;
  left: 0;
  color: #a3e635;
}

.rolling-text:hover .block {
  transform: translateY(-100%);
}

/* Staggered letter animation can be re-introduced later if needed */
.letter {
  display: inline-block;
}
