import { motion, useAnimation } from "framer-motion";
import React, { useState } from "react";

const Featured = () => {
  const cards =[useAnimation(), useAnimation()];
  // const [isHovering, setHovering] = useState(false);

  const handleHover = (index)=>{
    cards[index].start({y:"0%"})
  }
  const handleHoverEnd = (index)=>{
    cards[index].start({y:"100%"})
  }

  return (
    <div className="w-full py-10 sm:py-16 lg:py-20 overflow-hidden">
      <motion.div
        initial={{y:"100%"}}
        whileInView={{y:"0%"}}
        transition={{duration:1}}
        className="w-full px-4 sm:px-8 lg:px-10 border-b-[1px] border-zinc-700 pb-10 sm:pb-16 lg:pb-20"
      >
        <h1 className="text-[8vw] sm:text-[6vw] lg:text-[5vw] font-custom3 tracking-tight">Featured</h1>
      </motion.div>

      <div className="px-4 sm:px-8 lg:px-10">
        <div className="cards w-full flex flex-col lg:flex-row gap-6 sm:gap-8 lg:gap-10 mt-6 sm:mt-8 lg:mt-10">
          
          
            <motion.div
              initial={{x:"-100%", opacity:0}}
              whileInView={{x:"0%", opacity:1}}
              transition={{ duration:1}}
              onHoverStart={()=>handleHover(0)}
              onHoverEnd={()=>handleHoverEnd(0)}
              className="cardcontainer relative w-full lg:w-1/2 h-[50vh] sm:h-[60vh] lg:h-[75vh]"
            >
              <h1 className="absolute hidden lg:flex overflow-hidden text-green-200 text-[4vw] sm:text-[5vw] lg:text-[6vw] font-custom tracking-tight left-full -translate-x-1/2 top-1/4 translate-y-1/2 z-[9] whitespace-nowrap py-2">
                {"Modern_Webdesigns".split("").map((item, index) => (
                  <motion.span
                    key={index}
                    initial={{ y: "100%" }}
                    animate={cards[0]}
                    transition={{
                      ease: [0.65, 0, 0.35, 1],
                      delay: index * 0.01,
                    }}
                    className="inline-block"
                  >
                    {item}
                  </motion.span>
                ))}
              </h1>
              <div className="w-full h-full overflow-hidden rounded-lg">
                <img
                  className="w-full h-full object-cover rounded-lg"
                  src="https://ochi.design/wp-content/uploads/2024/08/Frame-481692-1-663x551.png"
                  alt="Modern web design showcase"
                />
              </div>
            </motion.div>
         
            <motion.div
              initial={{x:"100%", opacity:0}}
              whileInView={{x:"0%", opacity:1}}
              transition={{ duration:1}}
              onHoverStart={()=>handleHover(1)}
              onHoverEnd={()=>handleHoverEnd(1)}
              className="cardcontainer relative w-full lg:w-1/2 h-[50vh] sm:h-[60vh] lg:h-[75vh]"
            >
              <h1 className="absolute hidden lg:flex overflow-hidden text-green-200 text-[4vw] sm:text-[5vw] lg:text-[6vw] font-custom tracking-tight right-1/2 -translate-x-1/6 top-1/4 translate-y-1/2 z-[9] whitespace-nowrap py-2">
                {"Responsiveness_Friendly".split("").map((item, index) => (
                  <motion.span
                    key={index}
                    initial={{ y: "100%" }}
                    animate={cards[1]}
                    transition={{
                      ease: [0.65, 0, 0.35, 1],
                      delay: index * 0.01,
                    }}
                    className="inline-block"
                  >
                    {item}
                  </motion.span>
                ))}
              </h1>
              <div className="w-full h-full overflow-hidden rounded-lg">
                <img
                  className="w-full h-full object-cover rounded-lg"
                  src="https://ochi.design/wp-content/uploads/2023/10/Fyde_Illustration_Crypto_2-663x551.png"
                  alt="Responsive design showcase"
                />
              </div>
            </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Featured;
