import React, { useEffect, useState } from "react";

const Eyes = () => {

    const [rotate, setRotate]= useState(0);


    useEffect(()=>{
        window.addEventListener("mousemove", (e)=>{
            let mouseX =e.clientX;
            let mouseY =e.clientY;

            let deltaX =mouseX- window.innerWidth/2;
            let deltaY = mouseY- window.innerHeight/2;

            var angle = Math.atan2(deltaY, deltaX) * (180/Math.PI);
            setRotate(angle-180);
        })
    })

  return (
    <div className="eyes w-full h-screen overflow-hidden">
      <div data-scroll data-scroll-section data-scroll-speed="-0.5" className='relative w-full h-full bg-cover bg-center bg-[url("https://ochi.design/wp-content/uploads/2022/05/Top-Viewbbcbv-1-1440x921.jpg")]'>
        <div className="absolute flex top-1/2 left-1/2 gap-4 sm:gap-6 lg:gap-10 -translate-x-[50%] -translate-y-[50%]">
          <div className="flex items-center justify-center w-[20vw] h-[20vw] sm:w-[18vw] sm:h-[18vw] lg:w-[15vw] lg:h-[15vw] rounded-full bg-zinc-100">
            <div className="relative w-1/2 h-1/2 rounded-full bg-zinc-900">
              <div style={{transform:`translate(-50%, -50%) rotate(${rotate}deg)`}} className="line absolute top-1/2 left-1/2 -translate-x-[50%] -translate-y-[50%] w-full h-6 sm:h-8 lg:h-10">
                <div className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 rounded-full bg-zinc-100"></div>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center w-[20vw] h-[20vw] sm:w-[18vw] sm:h-[18vw] lg:w-[15vw] lg:h-[15vw] rounded-full bg-zinc-100">
            <div className="relative w-1/2 h-1/2 rounded-full bg-zinc-900">
              <div style={{transform:`translate(-50%, -50%) rotate(${rotate}deg)`}} className="line absolute top-1/2 left-1/2 -translate-x-[50%] -translate-y-[50%] w-full h-6 sm:h-8 lg:h-10">
                <div className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 rounded-full bg-zinc-100"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Eyes;
