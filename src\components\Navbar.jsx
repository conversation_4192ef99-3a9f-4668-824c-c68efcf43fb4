import { motion } from "framer-motion";
import React, { useState } from "react";
import RollingText from "./RollingText";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="fixed z-[999] w-full px-4 sm:px-8 lg:px-20 py-4 sm:py-6 lg:py-8 font-custom3 flex justify-between items-center bg-zinc-900/80 backdrop-blur-sm">
      <div className="logo overflow-hidden">
        <motion.h1
          initial={{ y: "100%" }}
          animate={{ y: "0%" }}
          transition={{
            ease: [0.65, 0, 0.35, 1],
            duration: 2,
          }}
          className="text-lg sm:text-xl lg:text-2xl"
        >
          CYNERGY
        </motion.h1>
      </div>

      {/* Desktop Navigation */}
      <div className="hidden lg:flex links gap-6 xl:gap-10">
        {["Services", "Our Work", "About Us", "Insights", "Contact"].map(
          (item, index) => (
            <RollingText
              key={index}
              text={item}
              className={`font-light capitalize cursor-pointer ${
                index === 4 && "xl:ml-16"
              }`}
            />
          )
        )}
      </div>

      {/* Mobile Menu Button */}
      <button
        className="lg:hidden flex flex-col gap-1 w-6 h-6 justify-center items-center"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
      >
        <span className={`w-full h-0.5 bg-white transition-transform ${isMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></span>
        <span className={`w-full h-0.5 bg-white transition-opacity ${isMenuOpen ? 'opacity-0' : ''}`}></span>
        <span className={`w-full h-0.5 bg-white transition-transform ${isMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></span>
      </button>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="absolute top-full left-0 w-full bg-zinc-900/95 backdrop-blur-sm lg:hidden"
        >
          <div className="flex flex-col px-4 py-6 gap-4">
            {["Services", "Our Work", "About Us", "Insights", "Contact"].map(
              (item, index) => (
                <a
                  key={index}
                  className="text-lg font-light capitalize hover:text-green-200 transition-colors cursor-pointer py-2 border-b border-zinc-700 last:border-b-0"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item}
                </a>
              )
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Navbar;
