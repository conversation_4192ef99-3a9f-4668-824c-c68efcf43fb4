import { motion } from "framer-motion";

const About = () => {

  return (
    <div data-scroll data-scroll-section data-scroll-speed="-0.1" className="bg-yellow-100 text-black w-full p-4 sm:p-8 lg:p-20 rounded-t-3xl mt-0 overflow-hidden">

      <h1 className="font-custom3 text-[5vw] sm:text-[4vw] lg:text-[3vw] leading-[5.5vw] sm:leading-[4.5vw] lg:leading-[3vw] tracking-tight">
        Cynergy is a dynamic web development startup empowering ambitious businesses to elevate their online presence, attract customers, showcase innovative ideas, and build meaningful digital experiences that drive growth and success.
      </h1>
      <div className='w-full pt-6 sm:pt-8 lg:pt-10 border-t-[1px] mt-10 sm:mt-16 lg:mt-20 border-white flex flex-col lg:flex-row gap-6 lg:gap-0'>
        <motion.div
          initial={{x:"-100%", opacity:0}}
          whileInView={{x:"0%", opacity:1}}
          transition={{ duration:1}}
          className="w-full lg:w-1/2 lg:pr-8"
        >
          <h1 className="text-[6vw] sm:text-[5vw] lg:text-[4vw] mb-4 sm:mb-6 lg:mb-8">Our approach:</h1>
          <button className="flex uppercase justify-center items-center gap-3 sm:gap-5 px-6 sm:px-8 lg:px-10 py-3 sm:py-4 lg:py-5 bg-green-100 text-zinc-900 rounded-full text-sm sm:text-base hover:bg-green-200 transition-colors">
            Read More
            <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-zinc-900"></div>
          </button>
        </motion.div>
        <motion.div
          initial={{x:"100%", opacity:0}}
          whileInView={{x:"0%", opacity:1}}
          transition={{ duration:1}}
          className="w-full lg:w-1/2 h-[40vh] sm:h-[50vh] lg:h-[70vh] bg-white rounded-3xl overflow-hidden"
        >
          <img
            className="w-full h-full object-cover rounded-3xl"
            src="https://ochi.design/wp-content/uploads/2022/05/Homepage-Photo-663x469.jpg"
            alt="Team collaboration and creative workspace"
          />
        </motion.div>
      </div>
    </div>
  );
};

export default About;
