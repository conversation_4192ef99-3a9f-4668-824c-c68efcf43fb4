import { easeIn, easeOut, motion } from "framer-motion";
import React from "react";
import { FaArrowUpLong } from "react-icons/fa6";

const LandingPage = () => {

  return (
    <div data-scroll data-scroll-section data-scroll-speed="-1" className="w-full min-h-screen bg-zinc-900 pt-2 overflow-hidden">
      <div data-scroll data-scroll-speed="0.4" className="textstructure mt-32 sm:mt-40 lg:mt-52 px-4 sm:px-8 lg:px-20">
        {["We Create", "Eye Opening", "Websites"].map((item, index) => {
          return (
            <div key={index} className="masker">
               <div className="w-fit pb-1 flex items-center overflow-hidden">
                  {index === 1 && (
                     <motion.div
                       initial={{width:0}}
                       animate={{width:"9vw"}}
                       transition={{ease:[0.45, 0, 0.55, 1], duration:2}}
                       className="mr-2 sm:mr-3 lg:mr-5 w-[12vw] sm:w-[10vw] lg:w-[8vw] h-[8vw] sm:h-[6.5vw] lg:h-[5.7vw] top-1 rounded-md relative bg-green-200 overflow-hidden flex-shrink-0"
                     >
                      <img
                        className="object-cover object-left w-full h-full rounded-lg"
                        src="https://images.unsplash.com/photo-1437750769465-301382cdf094?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OTJ8fHVuc3BsYXNoJTIwd2Vic2l0ZXxlbnwwfHwwfHx8MA%3D%3D"
                        alt="Creative design showcase"
                      />
                     </motion.div>
                  )}
              <h1 className="uppercase text-[12vw] sm:text-[10vw] lg:text-[8vw] font leading-[10vw] sm:leading-[8vw] lg:leading-[6vw] font-custom">
                {item}
              </h1>
              </div>
            </div>
          );
        })}
      </div>
      <motion.div
        initial={{x:"-100%"}}
        animate={{x:"0"}}
        transition={{ease: easeOut, duration:2}}
        className="border-t-[1px] border-zinc-600 flex flex-col lg:flex-row justify-between items-start lg:items-center py-6 sm:py-8 lg:py-10 px-4 sm:px-8 lg:px-20 mt-10 sm:mt-16 lg:mt-20 gap-4 lg:gap-0"
      >
        <div className="flex flex-col sm:flex-row gap-4 sm:gap-8 lg:gap-16">
          {[
            "For Public and Private Companies",
            "From the first Idea to Online presence",
          ].map((item, index) => (
            <p key={index} className="text-sm sm:text-md font-light tracking-tight leading-none font-custom3">
              {item}
            </p>
          ))}
        </div>

        <motion.div
          initial={{ x: "200%", opacity: 0 }}
          animate={{ x: "0%", opacity: 1 }}
          transition={{ ease: [0.45, 0, 0.55, 1], duration: 0.8, delay: 2 }}
          whileHover={{ backgroundColor: "#ffffff", color: "#18181b", transition: { duration: 0.5 } }}
          className="overflow-hidden h-8 sm:h-10 w-20 sm:w-24 hover:cursor-pointer transition-all border-zinc-200 border-[1px] rounded-full flex justify-center items-center text-xs sm:text-sm gap-2 sm:gap-5 px-3 sm:px-5 uppercase flex-shrink-0"
          style={{ backgroundColor: "#18181b", color: "#ffffff", transition: "background-color 0.01s ease, color 0.01s ease" }}
        >
          <motion.div
            initial={{ y: "200%" }}
            animate={{ y: "0%"}}
            transition={{ ease: [0.45, 0, 0.55, 1], delay: 1, duration: 0.8 }}
          >
            Start
          </motion.div>
          <motion.span
            initial={{ y: "200%" }}
            animate={{ y: "0%", rotate: "45deg" }}
            transition={{ ease: [0.45, 0, 0.55, 1], delay: 3, duration: 0.8 }}
            className="rotate-[45deg]"
          >
            <FaArrowUpLong />
          </motion.span>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default LandingPage;
