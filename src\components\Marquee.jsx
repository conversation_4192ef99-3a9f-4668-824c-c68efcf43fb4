import React from 'react'
import {motion} from 'framer-motion'

const Marquee = () => {

  return (
    <div data-scroll data-scroll-section data-scroll-speed="-0.1" className='w-full py-3 sm:py-5 bg-green-900 text-zinc-100 rounded-t-[1vw] mb-10 sm:mb-20 overflow-hidden'>
      <div className='text border-t-2 border-b-2 border-zinc-600 flex whitespace-nowrap leading-none overflow-hidden'>
        <motion.h1
          initial={{x:"0"}}
          animate={{x:"-100%"}}
          transition={{ease:"linear", repeat:Infinity, duration:15}}
          className='text-[22vw] sm:text-[20vw] lg:text-[15vw] leading-none font-custom font-semibold pt-2 uppercase pr-5 sm:pr-10 flex-shrink-0'
        >
          We are Cynergy
        </motion.h1>
        <motion.h1
          initial={{x:"0"}}
          animate={{x:"-100%"}}
          transition={{ease:"linear", repeat:Infinity, duration:15}}
          className='text-[22vw] sm:text-[20vw] lg:text-[15vw] leading-none font-custom font-semibold pt-2 uppercase pr-5 sm:pr-10 flex-shrink-0'
        >
          We are Cynergy
        </motion.h1>
        <motion.h1
          initial={{x:"0"}}
          animate={{x:"-100%"}}
          transition={{ease:"linear", repeat:Infinity, duration:15}}
          className='text-[22vw] sm:text-[20vw] lg:text-[15vw] leading-none font-custom font-semibold pt-2 uppercase pr-5 sm:pr-10 flex-shrink-0'
        >
          We are Cynergy
        </motion.h1>
        <motion.h1
          initial={{x:"0"}}
          animate={{x:"-100%"}}
          transition={{ease:"linear", repeat:Infinity, duration:15}}
llassName='text-[22vw] sm:text-[20vw] lg:text-[15vw] leading-none font-custom font-semibold pt-2 uppercase pr-5 sm:pr-10 flex-shrink-0'
        >
          We are Cynergy
        </motion.h1>
      </div>
    </div>
  )
}

export default Marquee