import React from "react";

const Cards = () => {
  return (
    <div className="w-full min-h-screen bg-zinc-900 flex flex-col lg:flex-row justify-center items-center gap-3 sm:gap-5 px-4 sm:px-8 lg:px-16 xl:px-32 py-8 lg:py-0">
      <div className="cardcontainer h-[40vh] sm:h-[50vh] w-full lg:w-1/2">
        <div data-scroll data-scroll-speed="0.1" className="card w-full h-full rounded-lg bg-green-100 overflow-hidden">
          <img
            className="w-full h-full object-cover rounded-lg"
            src="https://ochi.design/wp-content/uploads/2022/12/PB-Front-4-663x551.png"
            alt="Portfolio project showcase"
          />
        </div>
      </div>
      <div className="cardcontainer h-[40vh] sm:h-[50vh] w-full lg:w-1/2 flex flex-col sm:flex-row gap-3 sm:gap-5">
        <div data-scroll data-scroll-speed="-0.1" className="card w-full sm:w-1/2 h-1/2 sm:h-full rounded-lg bg-green-400 overflow-hidden">
          <img
            className="object-cover object-right w-full h-full rounded-lg"
            src="https://images.unsplash.com/photo-1645557052543-aeef271fbd53?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTl8fHVuc3BsYXNoJTIwd2Vic2l0ZXxlbnwwfHwwfHx8MA%3D%3D"
            alt="Web design example"
          />
        </div>
        <div data-scroll data-scroll-speed="0.1" className="card w-full sm:w-1/2 h-1/2 sm:h-full rounded-lg bg-green-200 overflow-hidden">
          <img
            className="object-cover object-left w-full h-full rounded-lg"
            src="https://plus.unsplash.com/premium_photo-1720588846590-0a634175efe7?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OTF8fHVuc3BsYXNoJTIwd2Vic2l0ZXxlbnwwfHwwfHx8MA%3D%3D"
            alt="Creative design showcase"
          />
        </div>
      </div>
    </div>
  );
};

export default Cards;
